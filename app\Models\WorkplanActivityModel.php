<?php
// app/Models/WorkplanActivityModel.php

namespace App\Models;

use CodeIgniter\Model;

/**
 * WorkplanActivityModel
 *
 * Handles database operations for the workplan_activities table.
 * This model has been updated to match the current database structure.
 */
class WorkplanActivityModel extends Model
{
    protected $table            = 'workplan_activities';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;

    // Fields that can be set during save/insert/update
    protected $allowedFields    = [
        'workplan_id',
        'branch_id',
        'activity_code',
        'title',
        'description',
        'activity_type',
        'q_one',
        'q_two',
        'q_three',
        'q_four',
        'total_budget',
        'rated_at',
        'rated_by',
        'rating',
        'reated_remarks',
        'supervisor_id',
        'status',
        'status_by',
        'status_at',
        'status_remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'workplan_id'      => 'required|integer',
        'branch_id'        => 'permit_empty|integer',
        'activity_code'    => 'permit_empty|max_length[20]|is_unique[workplan_activities.activity_code,id,{id}]',
        'title'            => 'required|max_length[255]',
        'description'      => 'permit_empty|string',
        'activity_type'    => 'required|in_list[training,inputs,infrastructure,output]',
        'q_one'            => 'permit_empty|decimal',
        'q_two'            => 'permit_empty|decimal',
        'q_three'          => 'permit_empty|decimal',
        'q_four'           => 'permit_empty|decimal',
        'total_budget'     => 'permit_empty|decimal',
        'rated_at'         => 'permit_empty|valid_date',
        'rated_by'         => 'permit_empty|integer',
        'rating'           => 'permit_empty|integer',
        'reated_remarks'   => 'permit_empty|string',
        'supervisor_id'    => 'permit_empty|integer',
        'status'           => 'permit_empty|max_length[50]',
        'status_by'        => 'permit_empty|integer',
        'status_at'        => 'permit_empty|valid_date',
        'status_remarks'   => 'permit_empty|string',
        'created_by'       => 'permit_empty|integer',
        'updated_by'       => 'permit_empty|integer',
        'deleted_by'       => 'permit_empty|integer'
    ];

    protected $validationMessages = [
        'workplan_id' => [
            'required' => 'Workplan ID is required',
            'integer' => 'Workplan ID must be a valid integer'
        ],
        'activity_code' => [
            'max_length' => 'Activity code cannot exceed 20 characters',
            'is_unique' => 'Activity code must be unique'
        ],
        'title' => [
            'required' => 'Activity title is required',
            'max_length' => 'Title cannot exceed 255 characters'
        ],
        'activity_type' => [
            'required' => 'Activity type is required',
            'in_list' => 'Activity type must be either training, inputs, infrastructure, or output'
        ],
        'q_one' => [
            'decimal' => 'Quarter One target must be a valid decimal number'
        ],
        'q_two' => [
            'decimal' => 'Quarter Two target must be a valid decimal number'
        ],
        'q_three' => [
            'decimal' => 'Quarter Three target must be a valid decimal number'
        ],
        'q_four' => [
            'decimal' => 'Quarter Four target must be a valid decimal number'
        ],
        'total_budget' => [
            'decimal' => 'Total budget must be a valid decimal number'
        ],
        'rating' => [
            'integer' => 'Rating must be a valid integer'
        ]
    ];

    /**
     * Get activities with their related workplan information
     *
     * @param array $conditions Optional conditions for filtering
     * @return array
     */
    public function getActivitiesWithWorkplan($conditions = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select($this->table . '.*, workplans.title as workplan_title');
        $builder->join('workplans', 'workplans.id = ' . $this->table . '.workplan_id');

        if (!empty($conditions)) {
            $builder->where($conditions);
        }

        return $builder->get()->getResultArray();
    }

    /**
     * Get activities by branch
     *
     * @param int $branchId
     * @return array
     */
    public function getActivitiesByBranch($branchId)
    {
        return $this->where('branch_id', $branchId)
                    ->where('deleted_at IS NULL')
                    ->findAll();
    }

    /**
     * Get activities by supervisor
     *
     * @param int $supervisorId
     * @return array
     */
    public function getActivitiesBySupervisor($supervisorId)
    {
        return $this->where('supervisor_id', $supervisorId)
                    ->where('deleted_at IS NULL')
                    ->findAll();
    }

    /**
     * Get activities with detailed information
     *
     * @return array
     */
    public function getActivitiesWithDetails()
    {
        $builder = $this->db->table($this->table . ' as wa');
        $builder->select([
            'wa.*',
            'w.title as workplan_title',
            'w.year as workplan_year',
            'b.name as branch_name',
            'CONCAT(s.fname, " ", s.lname) as supervisor_name',
            'CONCAT(u.fname, " ", u.lname) as created_by_name'
        ]);
        $builder->join('workplans as w', 'w.id = wa.workplan_id', 'left');
        $builder->join('branches as b', 'b.id = wa.branch_id', 'left');
        $builder->join('users as s', 's.id = wa.supervisor_id', 'left');
        $builder->join('users as u', 'u.id = wa.created_by', 'left');
        $builder->where('wa.deleted_at IS NULL');
        $builder->orderBy('wa.activity_code', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Generate activity code automatically
     * Pattern: ACT + last 2 digits of year + increment
     * Example: ACT24001, ACT24002, etc.
     *
     * @return string
     */
    public function generateActivityCode()
    {
        // Get current year's last 2 digits
        $yearSuffix = date('y');

        // Get the highest existing code for this year
        $prefix = 'ACT' . $yearSuffix;

        $builder = $this->db->table($this->table);
        $builder->select('activity_code');
        $builder->like('activity_code', $prefix, 'after');
        $builder->orderBy('activity_code', 'DESC');
        $builder->limit(1);

        $result = $builder->get()->getRowArray();

        if ($result && !empty($result['activity_code'])) {
            // Extract the numeric part and increment
            $lastCode = $result['activity_code'];
            $numericPart = (int) substr($lastCode, -3); // Get last 3 digits
            $newIncrement = $numericPart + 1;
        } else {
            // First code for this year
            $newIncrement = 1;
        }

        // Format with leading zeros (3 digits)
        return $prefix . str_pad($newIncrement, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Override insert method to automatically generate activity code
     *
     * @param array $data
     * @param bool $returnID
     * @return bool|int
     */
    public function insert($data = null, bool $returnID = true)
    {
        // Generate activity code if not provided
        if (is_array($data) && !isset($data['activity_code'])) {
            $data['activity_code'] = $this->generateActivityCode();
        }

        return parent::insert($data, $returnID);
    }

    /**
     * Override save method to handle activity code generation for new records
     *
     * @param array $data
     * @return bool
     */
    public function save($data): bool
    {
        // If this is a new record (no ID), generate activity code
        if (is_array($data) && (!isset($data['id']) || empty($data['id'])) && !isset($data['activity_code'])) {
            $data['activity_code'] = $this->generateActivityCode();
        }

        return parent::save($data);
    }
}